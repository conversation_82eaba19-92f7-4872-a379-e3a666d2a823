from typing import List
import json
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import StreamingResponse
from app.db.schema import SessionLocal
from app.models.llm import RequestModel, ResponseModel
from app.services.LLMService import LLMService
from typing import Optional
from app.data.LidarData import latest_lidar_data, data_lock
from app.prompts.llm import AVAILABLE_PROMPTS

router = APIRouter()

# ------------------------
# Endpoint using schemas
# ------------------------

# def get_relevant_context():
#     if not latest_lidar_data:
#         return {"error": "No data available"}
#     print("Total Frames of Data Available: ", len(latest_lidar_data))
#     return latest_lidar_data[0]  # most recent

def get_relevant_context():
    if not latest_lidar_data:
        return {"error": "No data available"}
    print("Total Frames of Data Available: ", len(latest_lidar_data))
    return latest_lidar_data[0]  # most recent


llm = LLMService()

@router.get("/generate", response_model=ResponseModel)
def generate(prompt: str = Query(...), context: Optional[str] = Query(None),
             model_name: Optional[str] = Query(None), system_prompt: Optional[str] = Query(None)):

    try:
        context = get_relevant_context()
        response_text = llm.generate_llm_response(prompt, context, model_name, system_prompt)
    except Exception as e:
        error_msg = str(e)
        # Return 404 for model not found errors
        if "not found" in error_msg.lower() and "model" in error_msg.lower() and "status code: 404" in error_msg:
            raise HTTPException(status_code=404, detail=error_msg)
        raise HTTPException(status_code=500, detail=str(e))

    return ResponseModel(response=response_text)


@router.post("/generate", response_model=ResponseModel)
def generate(request: RequestModel):
    try:
        context = get_relevant_context()
        response_text = llm.generate_llm_response(request.prompt, context, request.model_name, request.system_prompt)
    except Exception as e:
        error_msg = str(e)
        # Return 404 for model not found errors
        if "not found" in error_msg.lower() and "model" in error_msg.lower() and "status code: 404" in error_msg:
            raise HTTPException(status_code=404, detail=error_msg)
        raise HTTPException(status_code=500, detail=str(e))

    return ResponseModel(response=response_text)


@router.get("/generate-stream")
def generate_stream(prompt: str, model_name: Optional[str] = Query(None), system_prompt: Optional[str] = Query(None)):
    context = get_relevant_context()
    # Generator that yields context first, then LLM stream
    def generate():
        # Send context first
        yield f"**📋 Context:**\n```\n{context}\n```\n\n**🤖 Response:**\n\n"
        # Then stream LLM response
        yield from llm.stream_llm_response(prompt, context, model_name, system_prompt)
    return StreamingResponse(generate(), media_type="text/markdown")


@router.post("/generate-stream-with-context")
async def generate_stream(request: RequestModel):
    """Stream LLM response along with context."""
    prompt = request.prompt
    context = request.context
    model_name = request.model_name
    system_prompt = request.system_prompt

    def generate():
        yield f"**📋 Context:**\n```\n{context}\n```\n\n**🤖 Response:**\n\n"
        yield from llm.stream_llm_response(prompt, context, model_name, system_prompt)

    return StreamingResponse(generate(), media_type="text/markdown")


@router.get("/available-models")
async def get_available_models():
    """Get list of available models."""
    # These should match the models available in your Ollama instance
    available_models = ["llama3:8b", "gemma3:4b"]
    return {"models": available_models}


@router.get("/available-prompts")
async def get_available_prompts():
    """Get list of available system prompts."""
    return {"prompts": list(AVAILABLE_PROMPTS.keys())}