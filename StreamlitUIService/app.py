import os
import logging
import requests
import streamlit as st
from typing import List, Dict, Optional
import json
from prompts import templates

# -------------------------------
# Logging Setup
# -------------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s]: %(message)s"
)
logger = logging.getLogger(__name__)


# -------------------------------
# LidarService Class
# -------------------------------
import os
import requests
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class LidarService:
    """Handles communication with the Lidar LLM inference service."""

    def __init__(self, base_url: Optional[str] = None):
        self.base_url = base_url or os.environ.get("LIDAR_INFERENCE_SERVICE_URL")
        if not self.base_url:
            logger.warning("Environment variable LIDAR_INFERENCE_SERVICE_URL not set.")
        self.stream_endpoint = f"{self.base_url}/api/v1/generate-stream-with-context"
        self.context_endpoint = f"{self.base_url}/api/v1/get_lidar"
    
    def format_context_for_llm(self,context_json: dict) -> str:
        timestamp = context_json.get("timestamp", "N/A")
        context_info = context_json.get("context", "")

        formatted = (
            f"📅 Timestamp: {timestamp}\n\n"
            f"📝 Lidar Context:\n"
            f"{context_info.strip()}\n\n"
        )
        return formatted

    def _fetch_context(self) -> str:
        """Fetches Lidar context from the inference service."""
        try:
            response = requests.get(self.context_endpoint, timeout=15)
            response.raise_for_status()
            context_json = response.json()
            #context_str = json.dumps(context_json, indent=2)
            context_str = self.format_context_for_llm(context_json)
            return context_str
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch context: {e}")
            raise RuntimeError(f"Error fetching context from {self.context_endpoint}: {e}")

    def generate_stream(self, prompt: str, model_name: str = None, system_prompt: str = None, timeout: int = 60) -> str:
        """
        Sends prompt + context to the inference server via POST request
        and streams back the model’s response.
        """
        if not self.base_url:
            raise RuntimeError("LIDAR_INFERENCE_SERVICE_URL is not configured.")

        context = self._fetch_context()
        payload = {
            "prompt": prompt,
            "context": context,
            "model_name": model_name,
            "system_prompt": system_prompt
        }
        response_text = ""

        try:
            with requests.post(self.stream_endpoint, json=payload, stream=True, timeout=timeout) as response:
                response.raise_for_status()

                logger.info(f"Streaming response from {self.stream_endpoint} for prompt: {prompt[:50]}...")
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        decoded_chunk = chunk.decode("utf-8", errors="ignore")
                        response_text += decoded_chunk
                return response_text

        except requests.exceptions.Timeout:
            logger.error("Request to Lidar inference service timed out.")
            raise RuntimeError("The request to Lidar inference service timed out.")
        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error during inference request: {e}")
            # Parse specific error messages for better user feedback
            if hasattr(e.response, 'status_code') and e.response.status_code == 404:
                error_text = e.response.text if hasattr(e.response, 'text') else str(e)
                if 'not found' in error_text.lower() and 'model' in error_text.lower():
                    raise RuntimeError(f"MODEL_NOT_FOUND: {error_text}")
            raise RuntimeError(f"Error generating LLM response: {e}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Error during inference request: {e}")
            raise RuntimeError(f"Failed to connect to Lidar inference service: {e}")

    def get_available_models(self) -> List[str]:
        """Fetch available models from the backend."""
        if not self.base_url:
            return ["llama3:8b", "gemma3:4b"]  # Default fallback

        try:
            response = requests.get(f"{self.base_url}/api/v1/available-models", timeout=10)
            response.raise_for_status()
            return response.json().get("models", ["llama3:8b", "gemma3:4b"])
        except Exception as e:
            logger.warning(f"Failed to fetch available models: {e}")
            return ["llama3:8b", "gemma3:4b"]  # Default fallback

    def get_available_prompts(self) -> List[str]:
        """Fetch available prompts from the backend."""
        if not self.base_url:
            return list(templates.AVAILABLE_PROMPTS.keys())  # Default fallback

        try:
            response = requests.get(f"{self.base_url}/api/v1/available-prompts", timeout=10)
            response.raise_for_status()
            return response.json().get("prompts", list(templates.AVAILABLE_PROMPTS.keys()))
        except Exception as e:
            logger.warning(f"Failed to fetch available prompts: {e}")
            return list(templates.AVAILABLE_PROMPTS.keys())  # Default fallback

# -------------------------------
# LidarChatApp Class
# -------------------------------
class LidarChatApp:
    """Streamlit application for interacting with Lidar LLM."""

    def __init__(self):
        self.service = LidarService()
        self.available_models = self.service.get_available_models()
        self.available_prompts = self.service.get_available_prompts()
        self._init_session_state()

    def _init_session_state(self):
        """Initializes Streamlit session state."""
        if "messages" not in st.session_state:
            st.session_state.messages: List[Dict[str, str]] = []

    def _render_sidebar(self):
        """Renders sidebar components and controls."""
        with st.sidebar:
            st.image("images/TU_Berlin.png", width="stretch")

            # ---------------------------
            # Model selection
            # ---------------------------
            st.subheader("🤖 Select Model")
            selected_model = st.selectbox(
                "Choose Lidar Model:",
                self.available_models,
                index=0 if self.available_models else 0,
                key="selected_model"
            )

            # Show model availability status
            if selected_model:
                # Try to get fresh list of available models for validation
                try:
                    current_available = self.service.get_available_models()
                    if selected_model in current_available:
                        st.success(f"✅ Model '{selected_model}' is available")
                    else:
                        st.warning(f"⚠️ Model '{selected_model}' may not be available")
                        if current_available:
                            st.info("Available models: " + ", ".join(current_available))
                except Exception:
                    st.info("ℹ️ Could not verify model availability")

            # ---------------------------
            # Prompt selection
            # ---------------------------
            st.subheader("💬 Choose System Prompt")
            selected_prompt = st.selectbox(
                "Pick a System Prompt:",
                self.available_prompts,
                index=0 if self.available_prompts else 0,
                key="selected_prompt"
            )

            st.header("⚙️ Controls")

            if st.button("🗑️ Clear Chat"):
                st.session_state.messages.clear()
                st.rerun()

            st.subheader("🌍 Environment")
            st.markdown(
                f"""
                <div><b>LIDAR_INFERENCE_SERVICE_URL:</b>
                <span>{self.service.base_url or 'Not Set'}</span></div>
                <div><b>Selected Model:</b>
                <span>{st.session_state.get('selected_model', 'Not Selected')}</span></div>
                <div><b>Selected Prompt:</b>
                <span>{st.session_state.get('selected_prompt', 'Not Selected')}</span></div>
                """,
                unsafe_allow_html=True,
            )

            st.divider()

            st.markdown(
                """
                **📄 About**
                
                This demo showcases a conversational interface with the **Lidar LLM service**, 
                developed for research and educational purposes.
                
                © 2025 **TU Berlin**  
                All rights reserved.
                """
            )

    def _display_chat_history(self):
        """Displays the chat history from session state."""
        for msg in st.session_state.messages:
            with st.chat_message(msg["role"]):
                st.markdown(msg["content"])

    def _handle_user_input(self, prompt: str):
        """Handles user input and generates assistant response."""
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user", avatar="👤"):
            st.markdown(prompt)

        # Get selected model and prompt from session state
        selected_model = st.session_state.get("selected_model", self.available_models[0] if self.available_models else "llama3:8b")
        selected_prompt = st.session_state.get("selected_prompt", self.available_prompts[0] if self.available_prompts else "Autonomous Driving Assistant")

        with st.chat_message("assistant", avatar="📡"):
            response_placeholder = st.empty()
            response_text = ""

            with st.spinner("🚗 *Analyzing surroundings...*"):
                try:
                    response_text = self.service.generate_stream(prompt, selected_model, selected_prompt)
                    response_placeholder.markdown(response_text)
                except Exception as e:
                    error_str = str(e)

                    # Handle model not found errors specifically
                    if "MODEL_NOT_FOUND:" in error_str:
                        st.error("🚫 **Model Not Available**")
                        st.warning(f"The selected model **'{selected_model}'** is not available on the server.")

                        # Show available models
                        available_models = self.service.get_available_models()
                        if available_models:
                            st.info("📋 **Available models:**")
                            for model in available_models:
                                st.write(f"• {model}")
                            st.info("💡 **Tip:** Please select one of the available models from the sidebar and try again.")
                        else:
                            st.error("❌ Could not fetch available models from the server.")

                        response_text = f"❌ Model '{selected_model}' not available. Please select a different model."
                    else:
                        # Generic error handling
                        error_msg = f"❌ Error: {error_str}"
                        st.error(error_msg)
                        response_text = error_msg

        st.session_state.messages.append({"role": "assistant", "content": response_text})

    def run(self):
        """Runs the Streamlit app."""
        st.set_page_config(page_title="TU Berlin - Lidar LLM", layout="wide")
        st.title("🚗  LidarLLM")
        st.markdown("Chat with the Lidar LLM — send prompts and get streamed responses in real time.")

        self._render_sidebar()
        self._display_chat_history()

        if prompt := st.chat_input("Ask something about Lidar data, driving scenes, or detections..."):
            self._handle_user_input(prompt)


# -------------------------------
# Main Entry Point
# -------------------------------
if __name__ == "__main__":
    try:
        app = LidarChatApp()
        app.run()
    except Exception as e:
        st.error(f"🚨 Critical Error: {e}")
        logger.exception("Application crashed.")
